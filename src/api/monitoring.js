import request from '@/utils/request'

export function slaEvents(data) {
  return request({
    url: '/itsm/api/v1/slaevents',
    method: 'POST',
    data,
  })
}

export function getPrometheusGraphic(data) {
  return request({
    url: '/prometheus/api/v1/query_range?' + data,
    method: 'GET',
  })
}

export function getMqmonitorConf(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/monitoring/api/v1/mq/conf',
    method: 'GET',
    params: params,
  })
}

export function postMqmonitorConf(data) {
  return request({
    url: '/monitoring/api/v1/mq/conf',
    method: 'post',
    data,
  })
}

export function asyncMqmonitorTopic() {
  return request({
    url: '/monitoring/api/v1/rocketmq/async/topic',
    method: 'post',
    timeout: 60000,
  })
}

export function pushMqmonitorConf() {
  return request({
    url: '/monitoring/api/v1/rocketmq/push',
    method: 'post',
    timeout: 60000,
  })
}

export function ruisuACDNData(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/monitoring/api/v1/ruisu/acdn',
    method: 'GET',
    params: params,
  })
}

export function ruisuIpByCountry(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/monitoring/api/v1/ruisu/ipcountry',
    method: 'GET',
    params: params,
  })
}

export function kafkaNginxError(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/kafka/nginx/error',
    method: 'GET',
    params: params,
  })
}

export function kafkaCodeError(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/kafka/code/error',
    method: 'GET',
    params: params,
  })
}

export function ExcludeIgnoreList(data) {
  return request({
    url: '/devmonitor/api/v1/exclude/ignore/list',
    method: 'post',
    data,
  })
}

export function kafkaCodeErrorQuery(data) {
  return request({
    url: '/devmonitor/api/v1/kafka/code/query',
    method: 'POST',
    data,
  })
}

export function kafkaCodeList(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/kafka/code/list',
    method: 'GET',
    params: params,
  })
}

export function kafkaAlertFire(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/kafka/alert/fire',
    method: 'GET',
    params: params,
  })
}

export function postKafkaAlertFire(data) {
  return request({
    url: '/devmonitor/api/v1/kafka/alert/fire',
    method: 'post',
    timeout: 60000,
    data,
  })
}

export function kafkaAlertRule(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/kafka/alert/rules',
    method: 'GET',
    params: params,
  })
}

export function postKafkaAlertRule(data) {
  return request({
    url: '/devmonitor/api/v1/kafka/alert/rules',
    method: 'post',
    timeout: 60000,
    data,
  })
}

export function kafkaIgnoreRules() {
  return request({
    url: '/devmonitor/api/v1/kafka/ignore/rules',
    method: 'get',
  })
}

export function postkafkaIgnoreRules(data) {
  return request({
    url: '/devmonitor/api/v1/kafka/ignore/rules',
    method: 'post',
    timeout: 60000,
    data,
  })
}

export function pushPinpointConf(data) {
  return request({
    url: '/monitoring/api/v1/pinpoint/push',
    method: 'post',
    timeout: 60000,
    data,
  })
}

export function getPinpointConf(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/monitoring/api/v1/pinpoint/conf',
    method: 'get',
    params,
  })
}

export function postPinpointConf(data) {
  return request({
    url: '/monitoring/api/v1/pinpoint/conf',
    method: 'post',
    timeout: 60000,
    data,
  })
}

export function getPinpointList() {
  return request({
    url: '/monitoring/api/v1/pinpoint/list',
    method: 'get',
  })
}

export function getCdnUrl() {
  return request({
    url: '/monitoring/api/v1/get/cdn/url',
    method: 'get',
  })
}

export function getKafkaEventNotify(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/kafka/event/notify',
    method: 'get',
    params,
  })
}

export function getKafkaEventNotifyQuery(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/kafka/event/notify/query',
    method: 'get',
    params,
  })
}

export function getMysqlSlowGroupedLogs(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/mysql/slow/grouped_logs',
    method: 'get',
    params,
  })
}

export function getMysqlInstance(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/devmonitor/api/v1/mysql/slow/get_instance',
    method: 'get',
    params,
  })
}

export function postMysqlSlowAdvice(data) {
  return request({
    url: '/devmonitor/api/v1/mysql/slow/advice',
    method: 'post',
    timeout: 60000,
    data,
  })
}

export function postStatisticsData(data) {
  return request({
    url: '/devmonitor/api/v1/mysql/slow/statistics',
    method: 'post',
    timeout: 60000,
    data,
  })
}

export function postBizMoniData(data) {
  return request({
    url: '/devmonitor/api/v1/biz/moni/data/query',
    method: 'post',
    timeout: 60000,
    data,
  })
}

export function getBizMonidataIds() {
  return request({
    url: '/devmonitor/api/v1/biz/moni/data/bizids',
    method: 'get',
  })
}

export function postBizMoniDataCompare(data) {
  return request({
    url: '/devmonitor/api/v1/biz/moni/data/compare',
    method: 'post',
    timeout: 60000,
    data,
  })
}

// 获取实时活动告警列表 (根据 告警中心API.md 修正路径)
export function getAlertsCenterActive(params) {
  return request({
    url: '/alertscenter/api/v1/active', // 修正路径为 /active
    method: 'get',
    params,
  })
}

export function getAlertsCenterHistory(params) {
  return request({
    url: '/alertscenter/api/v1/history',
    method: 'get',
    params,
  })
}

// 获取告警来源列表
export function getAlertCenterSources(params) {
  return request({
    url: '/alertscenter/api/v1/sources',
    method: 'get',
    params,
  })
}

// 告警趋势
export function getAlertsCenterTrend(params) {
  return request({
    url: '/alertscenter/api/v1/trend',
    method: 'get',
    params,
  })
}

// 添加恢复告警的 API 方法
export function recoverAlert(fingerprint) {
  return request({
    url: `/alertscenter/api/v1/${fingerprint}/over`,
    method: 'post',
  })
}

export function getAlertsCenterHealth(params) {
  return request({
    url: '/alertscenter/api/v1/healthboard',
    method: 'get',
    params,
  })
}

export function getNamespaceStats(params) {
  return request({
    url: '/alertscenter/api/v1/namespace/stats',
    method: 'get',
    params,
  })
}

// 获取健康看板V2数据
export function getAlertsCenterHealthV2(params) {
  return request({
    url: '/alertscenter/api/v1/healthboardV2',
    method: 'get',
    params,
  })
}

// 获取健康看板V3数据
export function getAlertsCenterHealthV3(params) {
  return request({
    url: '/alertscenter/api/v1/healthboardV3',
    method: 'get',
    params,
  })
}

export function getAlertsCenterPanel(params) {
  return request({
    url: '/alertscenter/api/v1/panel',
    method: 'get',
    params,
  })
}
