<template>
  <div class="health-board">
    <!-- 添加时间筛选表单 -->
    <div>
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <div class="filter-row">
          <el-form-item class="time-range-item" label="">
            <div class="time-range-container">
              <el-radio-group
                v-model="timeRangeType"
                @change="handleTimeRangeChange"
              >
                <el-radio-button label="15m">15分钟</el-radio-button>
                <el-radio-button label="30m">30分钟</el-radio-button>
                <el-radio-button label="1h">1小时</el-radio-button>
                <el-radio-button label="1d">1天</el-radio-button>
                <el-radio-button label="custom">自定义</el-radio-button>
              </el-radio-group>
              <el-date-picker
                v-if="timeRangeType === 'custom'"
                v-model="timeRange"
                :default-time="['00:00:00', '23:59:59']"
                class="custom-date-picker"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始时间"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="handleCustomTimeChange"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 状态统计卡片 -->
    <el-row :gutter="20" class="status-summary">
      <el-col v-for="stat in statusStats" :key="stat.label" :span="6">
        <el-card :body-style="{ padding: '15px' }" shadow="hover">
          <div class="stat-content">
            <div :class="stat.type" class="stat-icon">
              <i :class="stat.icon"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">{{ stat.label }}</div>
              <div class="stat-value">{{ stat.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 健康状态表格 -->
    <div class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        :expand-row-keys="expandedRows"
        :height="null"
        row-key="source"
        style="width: 100%"
        @expand-change="handleExpand"
      >
        <el-table-column label="告警来源" min-width="200">
          <template #default="{ row }">
            <template v-if="sourceTypes.includes(row.source)">
              <el-tag
                :type="getSourceType(row.source)"
                class="clickable-tag"
                @click="handleSourceClick(row)"
              >
                <span>{{ formatSource(row.source) }}</span>
              </el-tag>
            </template>
            <template v-else>
              <el-tag v-if="row.account_id" style="padding-left: 8px">
                {{ row.account_id }}
              </el-tag>
              <span style="padding-left: 8px">{{ row.name }}</span>
            </template>
          </template>
        </el-table-column>

        <el-table-column align="center" label="严重" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.severities && row.severities.includes('critical')"
              type="danger"
              size="small"
              class="clickable-tag"
              @click="handleSeverityClick(row.source, 'critical')"
            >
              {{ row.critical_count || 0 }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>

        <el-table-column align="center" label="警告" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.severities && row.severities.includes('warning')"
              type="warning"
              size="small"
              class="clickable-tag"
              @click="handleSeverityClick(row.source, 'warning')"
            >
              {{ row.warning_count || 0 }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>

        <el-table-column align="center" label="提示" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.severities && row.severities.includes('info')"
              type="info"
              size="small"
              class="clickable-tag"
              @click="handleSeverityClick(row.source, 'info')"
            >
              {{ row.info_count || 0 }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>

        <el-table-column align="center" label="子模块数" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.children && row.children.length"
              type="info"
              size="small"
            >
              {{ row.children.length }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>

        <el-table-column align="center" label="总数" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.source_count > 0"
              type="primary"
              size="small"
              class="clickable-tag"
              @click="handleTotalClick(row)"
            >
              {{ row.source_count }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import {
    getAlertCenterSources,
    getAlertsCenterHealth,
  } from '@/api/monitoring'
  import dayjs from 'dayjs'

  export default {
    name: 'HealthBoard',

    data() {
      const initialTimeRange = this.getTimeRange('1d')
      return {
        sourceTypes: [],
        loading: false,
        tableData: [],
        expandedRows: [],
        statusStats: [
          { label: '正常', value: 0, type: 'success', icon: 'el-icon-success' },
          { label: '告警', value: 0, type: 'danger', icon: 'el-icon-error' },
          {
            label: '数据不足',
            value: 0,
            type: 'info',
            icon: 'el-icon-warning',
          },
          { label: '未配置', value: 0, type: 'info', icon: 'el-icon-close' },
        ],
        refreshTimer: null,
        refreshInterval: 60000,
        timeRange: initialTimeRange,
        queryParams: {},
        timeRangeType: '1d',
      }
    },

    created() {
      this.$nextTick(() => {
        this.handleQuery()
      })

      this.startAutoRefresh()
    },

    beforeDestroy() {
      this.stopAutoRefresh()
    },

    methods: {
      handleCustomTimeChange(value) {
        if (value && value.length === 2) {
          this.timeRange = value
          this.handleQuery()
        }
      },

      getTimeRange(type) {
        const end = dayjs().tz()
        let start

        switch (type) {
          case '15m':
            start = end.subtract(15, 'minutes')
            break
          case '30m':
            start = end.subtract(30, 'minutes')
            break
          case '1h':
            start = end.subtract(1, 'hour')
            break
          case '1d':
            start = end.subtract(1, 'day')
            break
          default:
            start = end.subtract(15, 'minutes')
        }

        return [
          start.format('YYYY-MM-DD HH:mm:ss'),
          end.format('YYYY-MM-DD HH:mm:ss'),
        ]
      },

      handleTimeRangeChange(type) {
        this.timeRange = this.getTimeRange(type)
        this.handleQuery()
      },

      resetQuery() {
        this.timeRangeType = '15m'
        this.timeRange = this.getTimeRange('15m')
        this.handleQuery()
      },

      async handleQuery() {
        if (!this.timeRange || this.timeRange.length !== 2) {
          this.timeRange = this.getTimeRange(this.timeRangeType)
        }
        await this.fetchData()
      },

      async fetchData() {
        if (!this.timeRange || this.timeRange.length !== 2) {
          console.warn('Invalid time range, resetting to default')
          this.timeRange = this.getTimeRange('15m')
        }

        this.loading = true
        try {
          const params = {
            start_time: this.timeRange[0],
            end_time: this.timeRange[1],
          }

          const resSource = await getAlertCenterSources(params)
          if (
            resSource.status === 'success' &&
            resSource.data &&
            resSource.data.sources
          ) {
            this.sourceTypes = resSource.data.sources.map((item) => item.source)
          } else {
            throw new Error(resSource.message || '获取告警来源数据失败')
          }

          const response = await getAlertsCenterHealth(params)
          if (response.status === 'success' && response.data) {
            this.tableData = this.processTableData(response.data.items || [])
            this.updateStatusStats(response.data.statistics || {})
          } else {
            throw new Error(response.message || '获取数据失败')
          }
        } catch (error) {
          console.error('获取健康看板数据失败:', error)
          this.$message.error('获取数据失败：' + (error.message || '未知错误'))
          this.tableData = []
          this.updateStatusStats(null)
        } finally {
          this.loading = false
        }
      },

      updateStatusStats(statistics) {
        if (!statistics) {
          statistics = { OK: 0, ALARM: 0, NO_DATA: 0, NO_CONF: 0 }
        }

        this.statusStats = [
          {
            label: '已恢复',
            value: statistics.OK || 0,
            type: 'success',
            icon: 'el-icon-success',
          },
          {
            label: '告警',
            value: statistics.ALARM || 0,
            type: 'danger',
            icon: 'el-icon-error',
          },
          {
            label: '数据不足',
            value: statistics.NO_DATA || 0,
            type: 'info',
            icon: 'el-icon-warning',
          },
          {
            label: '未配置',
            value: statistics.NO_CONF || 0,
            type: 'info',
            icon: 'el-icon-close',
          },
        ]
      },

      getSeverityClass(severity) {
        const map = {
          critical: 'danger',
          warning: 'warning',
          info: 'info',
        }
        return map[severity] || 'info'
      },

      getSourceType(source) {
        const map = {
          custom: 'info',
          tencent_cloud: 'success',
          pinpoint: 'info',
        }
        return map[source] || 'info'
      },

      formatSource(source) {
        const map = {
          custom: '自定义',
          tencent_cloud: '腾讯云',
          pinpoint: 'Pinpoint',
        }
        return map[source] || source
      },

      startAutoRefresh() {
        this.refreshTimer = setInterval(() => {
          this.fetchData()
        }, this.refreshInterval)
      },

      stopAutoRefresh() {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
      },

      handleSeverityClick(source, severity) {
        const count = this.getCountBySeverity(source, severity)
        if (count > 0) {
          const [start_time, end_time] = this.timeRange
          this.$router
            .push({
              name: 'AlertCenter',
              query: {
                tab: 'active',
                severity: severity,
                source: source,
                start_time: dayjs(start_time).format('YYYY-MM-DD HH:mm:ss'),
                end_time: dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
              },
            })
            .catch((err) => {
              if (err.name !== 'NavigationDuplicated') {
                console.error('Navigation failed:', err)
              }
            })
        }
      },

      handleSourceClick(row) {
        const totalCount = this.getTotalCountBySource(row.source)

        if (totalCount > 0) {
          const [start_time, end_time] = this.timeRange

          this.$router
            .push({
              name: 'AlertCenter',
              query: {
                tab: 'active',
                source: row.source,
                status: 'ALARM',
                start_time,
                end_time,
              },
            })
            .catch((err) => {
              if (err.name !== 'NavigationDuplicated') {
                console.error('Navigation failed:', err)
              }
            })
        }
      },

      getCountBySeverity(source, severity) {
        const row = this.tableData.find((item) => item.source === source)
        if (!row) return 0

        switch (severity) {
          case 'critical':
            return row.critical_count || 0
          case 'warning':
            return row.warning_count || 0
          case 'info':
            return row.info_count || 0
          default:
            return 0
        }
      },

      getTotalCountBySource(source) {
        const row = this.tableData.find((item) => item.source === source)
        if (!row) return 0

        return (
          (row.critical_count || 0) +
          (row.warning_count || 0) +
          (row.info_count || 0)
        )
      },

      processTableData(data) {
        return data.map((item) => {
          const mainModule = {
            source: item.source,
            name: this.formatSource(item.source),
            severities: item.severities || [],
            critical_count: item.critical_count || 0,
            warning_count: item.warning_count || 0,
            info_count: item.info_count || 0,
            source_count: item.source_count || 0,

            children: (item.items || []).map((subItem) => ({
              source: subItem.name,
              name: subItem.name,
              account_id: subItem.account_id,
              severities: subItem.severities || [],
              critical_count: subItem.critical_count || 0,
              warning_count: subItem.warning_count || 0,
              info_count: subItem.info_count || 0,
            })),
          }
          return mainModule
        })
      },

      handleExpand(row, expanded) {
        if (expanded) {
          this.expandedRows.push(row.source)
        } else {
          const index = this.expandedRows.indexOf(row.source)
          if (index !== -1) {
            this.expandedRows.splice(index, 1)
          }
        }
      },
      handleTotalClick(row) {
        const totalCount = row.source_count || 0

        if (totalCount > 0) {
          const [start_time, end_time] = this.timeRange

          this.$router
            .push({
              name: 'AlertCenter',
              query: {
                tab: 'active',
                source: row.source,
                status: 'ALARM',
                severity: '', // 不指定级别，即"全部"
                start_time: dayjs(start_time).format('YYYY-MM-DD HH:mm:ss'),
                end_time: dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
              },
            })
            .catch((err) => {
              if (err.name !== 'NavigationDuplicated') {
                console.error('Navigation failed:', err)
              }
            })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .health-board {
    padding: 20px;

    .status-summary {
      margin-bottom: 20px;

      .stat-card {
        transition: all 0.3s;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
      }

      .stat-content {
        display: flex;
        align-items: center;
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
          font-size: 24px;
          color: white;
        }

        &.success {
          background-color: #67c23a;
        }

        &.danger {
          background-color: #f56c6c;
        }

        &.info {
          background-color: #909399;
        }
      }

      .stat-info {
        flex: 1;
      }

      .stat-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 5px;
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
      }
    }

    .table-card {
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      ::v-deep .el-table {
        max-height: none !important;
        font-size: 14px;
        border: none;

        .el-table__header th {
          font-size: 14px;
          font-weight: bold;
          color: #606266;
        }

        .el-table__body td {
          font-size: 14px;
        }
      }

      ::v-deep .el-table__expanded-cell {
        padding: 20px !important;
      }

      ::v-deep .el-table__body {
        min-height: 100px;
      }
    }

    ::v-deep .el-tag {
      border-radius: 4px;
      padding: 0 12px;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      border: none;
      transition: all 0.3s;

      &.clickable-tag {
        cursor: pointer;

        &:hover {
          opacity: 0.85;
          transform: translateY(-1px);
        }
      }

      &.el-tag--warning {
        background-color: #ffab2e;
        border-color: #ffab2e;
        color: white;

        &:hover {
          background-color: #ffab2e;
          border-color: #ffab2e;
          color: white;
        }
      }

      &.el-tag--danger {
        background-color: #fe495a;
        border-color: #fe495a;
        color: white;

        &:hover {
          background-color: #fe495a;
          border-color: #fe495a;
          color: white;
        }
      }

      &.el-tag--primary {
        background-color: #409eff;
        border-color: #409eff;
        color: white;

        &:hover {
          background-color: #409eff;
          border-color: #409eff;
          color: white;
        }
      }
    }

    @media (max-width: 768px) {
      .table-card {
        height: auto;
      }
    }

    .sub-table {
      margin: 10px 40px;

      ::v-deep .el-table__header {
        background-color: #f5f7fa;
      }

      ::v-deep .el-table__row {
        background-color: #ffffff;
      }
    }

    .filter-form .filter-row {
      margin-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }

    .filter-form .el-form-item {
      margin-right: 15px;
      margin-bottom: 5px;
    }

    .time-range-container {
      display: flex;
      align-items: center;

      .el-radio-group {
        margin-right: 16px;
      }
    }

    @media (max-width: 768px) {
      .time-range-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
    }
  }

  .no-data {
    color: #909399;
  }
</style>
