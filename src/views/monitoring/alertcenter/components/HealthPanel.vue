<template>
  <div class="health-panel">
    <!-- 顶部状态统计 -->
    <div class="status-header">
      <div class="status-summary">
        <div class="status-item">
          <span class="status-label">此刻:</span>
          <div class="status-group">
            <div class="status-value success">
              <img :src="statusIcons.healthy" class="status-icon" alt="正常" />
              <span>正常: {{ statusStats.healthy }}</span>
            </div>
            <div class="status-value info">
              <img :src="statusIcons.info" class="status-icon" alt="提示" />
              <span>提示: {{ statusStats.info }}</span>
            </div>
            <div class="status-value warning">
              <img :src="statusIcons.warning" class="status-icon" alt="警告" />
              <span>警告: {{ statusStats.warning }}</span>
            </div>
            <div class="status-value error">
              <img :src="statusIcons.error" class="status-icon" alt="严重" />
              <span>严重: {{ statusStats.error }}</span>
            </div>
            <div class="status-value critical">
              <img :src="statusIcons.critical" class="status-icon" alt="紧急" />
              <span>紧急: {{ statusStats.critical }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选器区域 -->
      <div class="filter-area">
        <!-- 数据源指示器 -->
        <div class="data-source-indicator">
          <el-tag
            :type="dataSource === 'api' ? 'success' : 'info'"
            size="small"
          >
            {{ getDataSourceText() }}
          </el-tag>
          <el-tooltip v-if="apiError" :content="apiError" placement="top">
            <i
              class="el-icon-warning"
              style="color: #e6a23c; margin-left: 4px"
            ></i>
          </el-tooltip>
        </div>

        <!-- 云产品筛选下拉选择器 -->
        <div class="product-selector">
          <el-select
            v-model="selectedProduct"
            placeholder="请选择云产品进行筛选"
            filterable
            clearable
            style="width: 240px"
            @change="handleProductChange"
          >
            <el-option label="全部" value="" />
            <el-option-group
              v-for="category in productCategories"
              :key="category.name"
              :label="category.name"
            >
              <el-option
                v-for="product in category.products"
                :key="product.value"
                :label="product.label"
                :value="product.value"
              />
            </el-option-group>
          </el-select>
        </div>

        <!-- 时间选择器 -->
        <div class="time-selector">
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            format="yyyy/MM/dd"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
          />
          <!-- 日期导航按钮 -->
          <div class="date-navigation">
            <el-button
              type="text"
              icon="el-icon-arrow-left"
              title="向今天方向（+7天）"
              class="nav-button"
              :disabled="isLeftArrowDisabled"
              @click="navigateDate(7)"
            />
            <el-button
              type="text"
              icon="el-icon-arrow-right"
              title="向过去方向（-7天）"
              class="nav-button"
              @click="navigateDate(-7)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 健康状态表格 -->
    <div class="health-table-container">
      <el-table
        ref="healthTable"
        :key="tableData.length"
        v-loading="loading"
        :data="displayTableData"
        stripe
        style="width: 100%"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: '600',
        }"
        :row-class-name="getRowClassName"
        :span-method="spanMethod"
        show-header
      >
        <!-- 产品名称列 -->
        <el-table-column
          prop="productName"
          label="产品名称"
          width="320"
          fixed="left"
        >
          <template #default="{ row }">
            <span v-if="row.isCategory" class="category-name">
              {{ row.categoryName }}
            </span>
            <span v-else class="product-name">{{ row.productName }}</span>
          </template>
        </el-table-column>

        <!-- 此刻列 -->
        <el-table-column prop="current" label="此刻" width="120" align="center">
          <template #default="{ row }">
            <div class="health-status">
              <div
                v-if="
                  getCurrentHealthStatuses(row) &&
                  getCurrentHealthStatuses(row).length > 0
                "
                class="status-icons"
              >
                <img
                  v-for="status in getCurrentHealthStatuses(row)"
                  :key="status"
                  :src="getStatusIcon(status)"
                  :alt="status"
                  class="health-icon"
                  :title="getStatusTitle(status)"
                  @error="handleIconError"
                />
              </div>
              <span v-else class="no-data">-</span>
            </div>
          </template>
        </el-table-column>

        <!-- 动态日期列 -->
        <el-table-column
          v-for="date in dateColumns"
          :key="date.key"
          :prop="date.key"
          :label="date.label"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div class="health-status">
              <div
                v-if="
                  getHealthStatuses(row, date.key) &&
                  getHealthStatuses(row, date.key).length > 0
                "
                class="status-icons"
              >
                <img
                  v-for="status in getHealthStatuses(row, date.key)"
                  :key="status"
                  :src="getStatusIcon(status)"
                  :alt="status"
                  class="health-icon"
                  :title="getStatusTitle(status)"
                  @error="handleIconError"
                />
              </div>
              <span v-else class="no-data">-</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import { getAlertsCenterPanel } from '@/api/monitoring'
  import dayjs from 'dayjs'
  import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'

  // 扩展 dayjs 插件
  dayjs.extend(isSameOrAfter)

  export default {
    name: 'HealthPanel',

    data() {
      return {
        loading: false,
        selectedDate: dayjs().format('YYYY-MM-DD'),
        selectedProduct: '', // 选中的产品
        statusStats: {
          healthy: 0,
          info: 0,
          warning: 0,
          error: 0,
          critical: 0,
        },
        tableData: [],
        filteredTableData: [], // 筛选后的表格数据
        dateColumns: [],
        // 当天实时健康状态数据
        todayStatusData: [], // 存储当天实时健康状态数据
        // 数据源相关
        dataSource: 'api', // 数据源类型：只使用 'api'
        apiError: null, // API错误信息
        // SVG图标URL - 调整为5种状态
        statusIcons: {
          // 健康
          healthy: '/static/icon/success.svg',
          // 提示
          info: '/static/icon/info.svg',
          // 警告
          warning: '/static/icon/warning.svg',
          // 严重
          error: '/static/icon/error.svg',
          // 紧急
          critical: '/static/icon/critical.svg',
        },
      }
    },

    computed: {
      // 从表格数据中动态提取产品分类
      productCategories() {
        const categoryMap = new Map()

        // 遍历表格数据，提取分类和产品信息
        this.tableData.forEach((row) => {
          if (!row.isCategory && row.category && row.productName) {
            if (!categoryMap.has(row.category)) {
              categoryMap.set(row.category, {
                name: row.category,
                products: []
              })
            }

            const category = categoryMap.get(row.category)
            // 避免重复添加相同的产品
            if (!category.products.find(p => p.value === row.productName)) {
              category.products.push({
                label: row.productName,
                value: row.productName
              })
            }
          }
        })

        return Array.from(categoryMap.values())
      },

      // 当前显示的表格数据
      displayTableData() {
        const data = this.selectedProduct
          ? this.filteredTableData
          : this.tableData
        console.log('displayTableData 长度:', data.length)
        console.log(
          'displayTableData 详细内容:',
          data.map((item) => ({
            productName: item.productName,
            category: item.category,
            isCategory: item.isCategory,
          }))
        )
        return data
      },

      // 左箭头按钮是否禁用（只有当选中日期已经是今天或未来时才禁用）
      isLeftArrowDisabled() {
        if (!this.selectedDate) return false

        const today = dayjs()
        const selectedDate = dayjs(this.selectedDate)

        // 只有当选择的日期已经是今天或未来日期时，才禁用左箭头
        // 允许从过去日期向今天方向移动，即使加7天会超过今天
        return selectedDate.isSameOrAfter(today, 'day')
      },
    },

    created() {
      // 初始化日期列
      this.initializeDateColumns()
      // 确保初始状态显示所有数据
      this.selectedProduct = ''
      this.filteredTableData = []
    },

    mounted() {
      // 组件挂载后获取真实数据
      this.fetchHealthData()

      // 调试：检查表格列渲染状态
      this.$nextTick(() => {
        this.debugTableColumns()
      })
    },

    methods: {
      // 初始化日期列（基于选定日期的7天范围，按日期倒序）
      initializeDateColumns() {
        const columns = []
        // 基于选定日期，从选定日期开始倒序排列7天
        const endDate = dayjs(this.selectedDate)
        for (let i = 0; i < 7; i++) {
          const date = endDate.subtract(i, 'day')
          columns.push({
            key: `day_${i}`,
            label: date.format('MM/DD'),
            date: date.format('YYYY-MM-DD'),
          })
        }
        console.log("colums:", columns);
        this.dateColumns = columns
        console.log(
          '日期列初始化完成:',
          columns.map((col) => col.date)
        )
      },
      // 获取健康状态
      getHealthStatus(row, dateKey) {
        return row[dateKey] && row[dateKey].hasData
      },

      // 获取状态样式类
      getStatusClass(row, dateKey) {
        if (!row[dateKey] || !row[dateKey].hasData) return ''

        switch (row[dateKey].status) {
          case 'healthy':
            return 'status-healthy'
          case 'warning':
            return 'status-warning'
          case 'error':
            return 'status-error'
          default:
            return 'status-healthy'
        }
      },

      // 处理日期变化
      handleDateChange(date) {
        this.selectedDate = date
        // 更新日期列以匹配新的选定日期
        this.initializeDateColumns()
        this.fetchHealthData()
      },

      // 日期导航功能 - 左箭头向今天方向，右箭头向过去方向
      navigateDate(days) {
        const currentDate = dayjs(this.selectedDate)
        const newDate = currentDate.add(days, 'day')
        const today = dayjs().format('YYYY-MM-DD')

        // 如果是向未来方向（左箭头，+7天），不能超过今天
        if (days > 0 && newDate.isAfter(today)) {
          this.selectedDate = today
        } else {
          this.selectedDate = newDate.format('YYYY-MM-DD')
        }

        // 更新日期列以匹配新的选定日期
        this.initializeDateColumns()
        this.fetchHealthData()
      },

      // 处理产品筛选变化
      handleProductChange(product) {
        this.selectedProduct = product
        if (product) {
          // 筛选指定产品的数据
          this.filteredTableData = this.tableData.filter(
            (row) => row.productName === product
          )
        } else {
          // 显示所有产品
          this.filteredTableData = []
        }
        // 筛选后重新统计
        this.updateStatusStats()
      },

      // 获取分类标签类型
      // getCategoryTagType(category) {
      //   const typeMap = {
      //     计算: 'primary',
      //     高性能计算: 'success',
      //     分布式云: 'warning',
      //     容器: 'info',
      //   }
      //   return typeMap[category] || 'default'
      // },

      // 获取当前健康状态数组（从实时状态数据中获取）
      getCurrentHealthStatuses(row) {
        // 如果是分类行，返回空数组
        if (row.isCategory) {
          return []
        }

        // 从当天实时状态数据中查找对应产品的状态
        const todayStatus = this.todayStatusData.find(
          item => item.productName === row.productName && item.category === row.category
        )

        return todayStatus && todayStatus.hasData ? todayStatus.statuses : []
      },

      // 获取健康状态数组
      getHealthStatuses(row, dateKey) {
        return row[dateKey] && row[dateKey].hasData ? row[dateKey].statuses : []
      },

      // 获取状态标题
      getStatusTitle(status) {
        const titleMap = {
          healthy: '正常',
          info: '提示',
          warning: '警告',
          error: '严重',
          critical: '紧急',
        }
        return titleMap[status] || status
      },

      // 获取状态图标URL
      getStatusIcon(status) {
        return this.statusIcons[status] || this.statusIcons.healthy
      },

      // 处理图标加载错误
      handleIconError(event) {
        console.warn('Health status icon failed to load:', event.target.src)
        event.target.style.display = 'none'
      },

      // 获取行样式类名
      getRowClassName({ row, rowIndex }) {
        if (row.isCategory) {
          return 'category-row'
        }
        return 'product-row'
      },

      // 合并单元格方法
      spanMethod({ row, column, rowIndex, columnIndex }) {
        // 如果是分类行，合并所有列
        if (row.isCategory) {
          if (columnIndex === 0) {
            // 产品名称列显示分类名称，占据所有列
            return [1, this.dateColumns.length + 3] // +3 是因为有产品名称、RSS、此刻列
          } else {
            // 其他列隐藏
            return [0, 0]
          }
        }
        return [1, 1]
      },

      // 获取健康数据
      async fetchHealthData() {
        this.loading = true
        this.apiError = null

        try {
          // 计算7天数据范围：从选定日期往前推6天到选定日期
          const endDate = dayjs(this.selectedDate)
          const startDate = endDate.subtract(6, 'day')

          const params = {
            date: this.selectedDate,
            start_time: startDate.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            end_time: endDate.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
          }
          console.log('请求参数 (7天范围):', params)
          console.log(
            `数据范围: ${startDate.format('YYYY-MM-DD')} 到 ${endDate.format(
              'YYYY-MM-DD'
            )}`
          )

          // 调用真实API
          const response = await getAlertsCenterPanel(params)

          if (response && response.data) {
            // 处理API返回的真实数据
            this.processApiData(response.data)
            console.log('获取到的真实数据：', response.data)
            this.dataSource = 'api'
            console.log('成功获取真实API数据')
          } else {
            throw new Error('API返回数据格式错误')
          }
        } catch (error) {
          console.error('获取健康数据失败:', error)
          this.apiError = error.message || '未知错误'

          // 显示错误提示
          this.$message.error('无法获取健康数据，请检查网络连接或稍后重试')
        } finally {
          this.loading = false
        }
      },

      // 更新状态统计（只统计"此刻"列的实时健康状态）
      updateStatusStats() {
        let healthy = 0
        let info = 0
        let warning = 0
        let error = 0
        let critical = 0

        // 确定要分析的数据源
        let dataToAnalyze = this.todayStatusData

        // 如果有产品筛选，则只统计筛选后的产品
        if (this.selectedProduct) {
          dataToAnalyze = this.todayStatusData.filter(
            item => item.productName === this.selectedProduct
          )
        }

        dataToAnalyze.forEach((item) => {
          // 只统计有数据的项目
          if (item.hasData && item.statuses) {
            item.statuses.forEach((status) => {
              switch (status) {
                case 'healthy':
                  healthy++
                  break
                case 'info':
                  info++
                  break
                case 'warning':
                  warning++
                  break
                case 'error':
                  error++
                  break
                case 'critical':
                  critical++
                  break
              }
            })
          }
        })

        this.statusStats = { healthy, info, warning, error, critical }
        console.log('状态统计更新（仅实时数据）:', this.statusStats)
      },

      // 处理API返回的真实数据
      processApiData(data) {
        try {
          console.log('处理API数据:', data)

          // 处理历史表格数据
          if (data.items && Array.isArray(data.items)) {
            this.tableData = this.transformApiDataToTableFormat(data.items)
          } else {
            // 如果API数据格式不符合预期，清空表格数据
            this.tableData = []
            console.warn('API数据格式不符合预期，无法显示表格数据')
          }

          // 处理当天实时状态数据
          if (data.today_status && Array.isArray(data.today_status)) {
            // 使用API返回的实时状态数据
            this.todayStatusData = this.processTodayStatusData(data.today_status)
            console.log('处理实时状态数据:', this.todayStatusData)
          } else {
            // 如果API没有返回实时状态数据，生成模拟数据（临时方案）
            this.generateMockTodayStatus()
            console.warn('API未返回实时状态数据，使用模拟数据')
          }

          // 根据实时状态数据重新计算统计（不再使用API返回的stats）
          this.updateStatusStats()

          // 重置筛选
          this.selectedProduct = ''
          this.filteredTableData = []

          // 强制刷新表格布局以确保所有列正确显示
          this.forceTableRefresh()
        } catch (error) {
          console.error('处理API数据时出错:', error)
          // 出错时清空数据并显示错误
          this.tableData = []
          this.todayStatusData = []
          this.apiError = error.message || '数据处理错误'
        }
      },

      // 将API数据转换为表格格式
      transformApiDataToTableFormat(apiItems) {
        const groupedData = []

        // 按分类分组API数据
        const categoryGroups = {}
        apiItems.forEach((item) => {
          const category = item.category || '未分类'
          if (!categoryGroups[category]) {
            categoryGroups[category] = []
          }
          categoryGroups[category].push(item)
        })

        // 为每个分类创建分组行和产品行
        Object.keys(categoryGroups).forEach((category) => {
          // 添加分类行
          const categoryRow = {
            isCategory: true,
            categoryName: category,
            productName: '',
            category: category,
          }

          // 为分类行添加空的历史状态数据（不再处理 current 字段）
          this.dateColumns.forEach((col) => {
            categoryRow[col.key] = { statuses: [], hasData: false }
          })

          groupedData.push(categoryRow)

          // 添加该分类下的产品行
          categoryGroups[category].forEach((item, index) => {
            // 如果是分类项，跳过此行
            if (item.isCategory) {
              return
            }

            console.log('处理产品项:', item)
            const row = {
              productName: item.product_name || item.productName || '未知产品',
              category: category,
              isCategory: false,
              // 注意：不再处理 current 字段，"此刻"列数据来自独立的实时状态数据
            }

            // 处理历史数据 - 修复日期字段映射
            this.dateColumns.forEach((col, index) => {
              // 将 date_2025_07_30, date_2025_07_31 格式映射到实际日期
              const actualDate = col.date.replace(/-/g, '_') // 2025-07-30 -> 2025_07_30
              const dateKey = `date_${actualDate}` // date_2025_07_30

              console.log(`映射日期: ${col.key} -> ${dateKey}`, item[dateKey])

              if (item[dateKey]) {
                row[col.key] = {
                  statuses: this.normalizeStatuses(
                    item[dateKey].statuses || ['healthy']
                  ),
                  hasData: item[dateKey].hasData || false,
                }
              } else {
                // 如果没有对应日期的数据，设置为默认值
                row[col.key] = {
                  statuses: ['healthy'],
                  hasData: false,
                }
              }
            })

            groupedData.push(row)
          })
        })

        console.log('转换后的表格数据:', groupedData)
        return groupedData
      },

      // 标准化状态数组
      normalizeStatuses(statuses) {
        if (!Array.isArray(statuses)) {
          return ['healthy']
        }

        // 确保状态值有效
        const validStatuses = [
          'healthy',
          'info',
          'warning',
          'error',
          'critical',
        ]
        return statuses.filter((status) => validStatuses.includes(status))
      },

      // 处理API返回的当天实时状态数据
      processTodayStatusData(apiTodayStatus) {
        const processedData = []

        apiTodayStatus.forEach((item) => {
          processedData.push({
            productName: item.product_name || item.productName || '未知产品',
            category: item.category || '未分类',
            statuses: this.normalizeStatuses(
              item.statuses || item.Status || ['healthy']
            ),
            hasData: item.hasData !== undefined ? item.hasData : true,
          })
        })

        return processedData
      },

      // 生成模拟的当天实时状态数据（临时方案）
      generateMockTodayStatus() {
        const mockData = []

        // 从表格数据中提取所有产品，为每个产品生成实时状态
        this.tableData.forEach((row) => {
          if (!row.isCategory && row.productName) {
            mockData.push({
              productName: row.productName,
              category: row.category,
              statuses: this.generateRandomStatuses(),
              hasData: true,
            })
          }
        })

        this.todayStatusData = mockData
        console.log('生成模拟实时状态数据:', mockData)
      },

      // 生成随机状态数组（用于模拟数据）
      generateRandomStatuses() {
        const statuses = []
        const random = Math.random()

        // 70% 概率只有健康状态
        if (random < 0.7) {
          statuses.push('healthy')
        } else {
          // 30% 概率有异常状态，可能有多个状态
          const possibleStatuses = ['info', 'warning', 'error', 'critical']
          const statusCount = Math.random() < 0.7 ? 1 : 2 // 70%概率1个状态，30%概率2个状态

          // 随机选择状态
          const shuffled = possibleStatuses.sort(() => 0.5 - Math.random())
          for (let i = 0; i < statusCount; i++) {
            if (shuffled[i]) {
              statuses.push(shuffled[i])
            }
          }
        }

        return statuses
      },

      // 获取数据源文本
      getDataSourceText() {
        return this.dataSource === 'api' ? '实时数据' : '数据加载中'
      },

      // 调试方法：手动触发实时状态数据更新
      debugUpdateTodayStatus() {
        console.log('=== 调试：手动更新实时状态数据 ===')
        this.generateMockTodayStatus()
        this.updateStatusStats()
        console.log('当前实时状态数据:', this.todayStatusData)
        console.log('状态统计:', this.statusStats)
      },

      // 调试方法：检查表格列显示状态
      debugTableColumns() {
        console.log('=== 调试：表格列显示状态 ===')
        console.log('dateColumns 数量:', this.dateColumns.length)
        console.log('dateColumns 详情:', this.dateColumns)
        console.log('表格数据行数:', this.displayTableData.length)

        // 检查表格DOM元素
        this.$nextTick(() => {
          const tableHeaders = document.querySelectorAll('.el-table__header th')
          console.log('实际渲染的表格列数:', tableHeaders.length)
          tableHeaders.forEach((header, index) => {
            console.log(`列 ${index}:`, header.textContent.trim())
          })

          // 检查表格容器宽度
          const tableContainer = document.querySelector('.health-table-container')
          const table = document.querySelector('.el-table')
          if (tableContainer && table) {
            console.log('容器宽度:', tableContainer.offsetWidth)
            console.log('表格宽度:', table.offsetWidth)
            console.log('表格滚动宽度:', table.scrollWidth)
          }
        })
      },

      // 强制刷新表格布局
      forceTableRefresh() {
        this.$nextTick(() => {
          // 触发表格重新计算布局
          if (this.$refs.healthTable) {
            this.$refs.healthTable.doLayout()
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .health-panel {
    padding: 0px 20px 12px 10px;
    background: #fff;
    overflow: visible;

    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px; /* 优化：从20px减少到16px */
      padding: 12px 20px; /* 优化：从15px减少到12px，减少垂直空间占用 */
      background: #f8f9fa;
      border-radius: 6px;

      .status-summary {
        .status-item {
          display: flex;
          align-items: center;
          gap: 20px;

          .status-label {
            font-weight: 600;
            color: #303133;
          }

          .status-group {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
          }

          .status-value {
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 500;

            .status-icon {
              width: 16px;
              height: 16px;
              object-fit: contain;
            }

            &.success {
              color: #67c23a;
            }

            &.info {
              color: #409eff;
            }

            &.warning {
              color: #e6a23c;
            }

            &.error {
              color: #f56c6c;
            }

            &.critical {
              color: #a8071a;
            }
          }
        }
      }

      .filter-area {
        display: flex;
        align-items: center;
        gap: 15px;

        .data-source-indicator {
          display: flex;
          align-items: center;
          gap: 4px;

          .el-tag {
            font-size: 12px;
            height: 24px;
            line-height: 22px;
          }
        }

        .product-selector {
          ::v-deep .el-input__inner {
            border-radius: 4px;
          }
        }

        .time-selector {
          display: flex;
          align-items: center;
          gap: 8px;

          ::v-deep .el-input__inner {
            border-radius: 4px;
          }

          .date-navigation {
            display: flex;
            align-items: center;
            gap: 4px;

            .nav-button {
              padding: 8px;
              min-width: 32px;
              height: 32px;
              border-radius: 4px;
              color: #606266;
              border: 1px solid #dcdfe6;
              background-color: #fff;
              transition: all 0.3s;

              &:hover:not(:disabled) {
                color: #409eff;
                border-color: #c6e2ff;
                background-color: #ecf5ff;
              }

              &:active:not(:disabled) {
                color: #3a8ee6;
                border-color: #3a8ee6;
              }

              &:disabled {
                color: #c0c4cc;
                border-color: #e4e7ed;
                background-color: #f5f7fa;
                cursor: not-allowed;
              }

              i {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    .health-table-container {
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      /* 移除所有高度限制，让表格完全由内容决定高度 */
      overflow-x: auto; /* 允许水平滚动以显示所有列 */

      ::v-deep .el-table {
        border: none; /* 移除表格外边框 */
        /* 表格高度完全自适应 */
        height: auto;
        padding: 0;

        /* 确保表格可以水平滚动显示所有列 */
        .el-table__body-wrapper {
          overflow-x: auto; /* 允许水平滚动 */
          overflow-y: visible; /* 垂直方向保持可见 */
        }

        /* 确保表格头部可以水平滚动 */
        .el-table__header-wrapper {
          overflow-x: auto; /* 允许头部水平滚动 */
          overflow-y: hidden;
          position: sticky;
          top: 0;
          z-index: 10;
        }

        /* 其他原有样式保持不变 */
        .category-tag {
          font-weight: 500;
          border-radius: 12px;
        }

        .product-name {
          font-weight: 500;
          color: #303133;
        }

        .rss-icon {
          color: #909399;
          font-size: 16px;
        }

        .batch-text {
          color: #606266;
          font-size: 12px;
        }

        .health-status {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 32px;

          .status-icons {
            display: flex;
            gap: 2px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
          }

          .health-icon {
            width: 18px;
            height: 18px;
            object-fit: contain;
            transition: transform 0.2s ease;

            &:hover {
              transform: scale(1.1);
            }
          }

          .no-data {
            color: #c0c4cc;
            font-size: 14px;
          }
        }

        // 分类行样式
        .category-name {
          font-weight: bold;
          color: #e74c3c;
          font-size: 14px;
          padding-left: 5px;
        }

        // 产品行样式
        .product-name {
          padding-left: 20px;
          color: #606266;
        }
      }

      // 分类行样式
      :deep(.category-row) {
        background-color: #fafafa !important;
        border-top: 2px solid #e74c3c;
        border-bottom: 1px solid #e74c3c;

        td {
          padding: 12px 0 !important;
          border-bottom: 1px solid #e74c3c !important;
        }
      }

      // 产品行样式
      :deep(.product-row) {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      ::v-deep .el-table__header-wrapper {
        overflow: visible !important;
      }

      ::v-deep .el-table__header th {
        background-color: #f5f7fa !important;
        color: #606266 !important;
        font-weight: 600 !important;
        height: 40px !important;
        padding: 8px 0 !important;
        border-bottom: 1px solid #ebeef5 !important;
      }

      ::v-deep .el-table__header th .cell {
        padding: 0 10px !important;
        line-height: 24px !important;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: ellipsis !important;
      }

      ::v-deep .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      // 去除固定列底部横线
      ::v-deep .el-table__fixed::before,
      ::v-deep .el-table__fixed-right::before {
        background-color: transparent !important;
      }
    }

    // 响应式设计
    @media (max-width: 768px) {
      padding: 10px;

      .status-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;

        .filter-area {
          flex-direction: column;
          width: 100%;
          gap: 10px;

          .product-selector {
            width: 100%;

            ::v-deep .el-select {
              width: 100%;
            }
          }
        }
      }

      .health-table-container {
        /* 移除容器级别的滚动，让表格自己管理 */
        overflow-x: auto; /* 允许水平滚动 */
        overflow-y: visible;

        /* 确保在小屏幕上表格可以正常显示 */
        .el-table {
          min-width: 1300px; /* 设置足够的最小宽度：320+120+120*7=1280px，留一些余量 */
        }
      }
    }
  }
</style>
