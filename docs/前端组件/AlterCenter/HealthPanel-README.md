# HealthPanel 组件文档

![状态](https://img.shields.io/badge/状态-活跃-brightgreen) ![版本](https://img.shields.io/badge/版本-4.0.0-blue) ![技术栈](https://img.shields.io/badge/Vue-2.x-green) ![UI框架](https://img.shields.io/badge/ElementUI-2.x-blue)

## 组件概述

HealthPanel 是AlertCenter的核心子组件，用于展示系统各个产品模块的健康状态信息。组件采用表格形式展示数据，支持按产品分类分组显示，提供日期导航和产品筛选功能。

**重要更新（v4.0.0）**：
- **"此刻"列重构**：现在显示当前系统时间所在日期的实时健康状态，独立于用户选定的历史日期
- **状态统计优化**：只统计"此刻"列的实时健康状态，不再包含历史数据
- **数据源分离**：实时状态数据与历史数据完全分离，提供更准确的当前状态信息

### 主要功能
- **实时健康状态展示**: "此刻"列显示当前系统时间的最新健康状态
- **历史状态查看**: 历史日期列显示用户选定时间范围内的健康状态记录
- **分类分组**: 按产品分类（计算、高性能计算、分布式云、容器）进行分组显示
- **实时状态统计**: 统计当前实时健康状态的数量分布（不包含历史数据）
- **多状态支持**: 支持产品同时显示多种健康状态（因为产品下的实例可能存在多重状态）
- **时间导航**: 支持按周切换查看不同时间段的历史健康数据
- **产品筛选**: 支持按产品名称筛选显示特定产品的健康状态
- **图标展示**: 使用SVG图标直观展示不同健康状态

### 技术栈
- **前端框架**: Vue 2.x
- **UI组件库**: Element UI 2.x
- **时间处理**: Day.js
- **样式预处理**: SCSS
- **图标资源**: 本地静态资源

## 组件结构

```mermaid
graph TD
  A[HealthPanel] --> B[状态统计区域]
  A --> C[筛选控制区域]
  A --> D[健康状态表格]
  
  B --> E[5种状态统计]
  C --> F[产品筛选器]
  C --> G[日期选择器]
  C --> H[日期导航按钮]
  
  D --> I[分类行]
  D --> J[产品行]
  D --> K[日期列]
  
  E --> L[正常/提示/警告/严重/紧急]
  I --> M[分类标签]
  J --> N[产品名称]
  J --> O[RSS图标]
  K --> P[多状态图标组]
```

## API 接口规范

### 1. 获取健康面板数据

```http
GET /alertscenter/api/v1/panel
```

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| date | string | 是 | 历史数据查询基准日期 | "2025-07-30" |
| start_time | string | 是 | 历史数据开始时间（7天范围） | "2025-07-24 00:00:00" |
| end_time | string | 是 | 历史数据结束时间 | "2025-07-30 23:59:59" |

#### 请求示例

```javascript
// 获取以2025-07-30为基准的7天历史数据 + 当天实时状态
const params = {
  date: "2025-07-30",                    // 基准日期
  start_time: "2025-07-24 00:00:00",     // 7天前开始时间
  end_time: "2025-07-30 23:59:59"        // 基准日期结束时间
}
```

#### 响应数据结构

```typescript
interface HealthPanelResponse {
  status: string;
  message: string;
  data: {
    items: HistoryHealthItem[];           // 历史健康数据（不包含"此刻"列数据）
    today_status: TodayStatusItem[];      // 当天实时健康状态数据（新增字段）
    dateRange: string[];
  };
}

// 历史健康数据项（用于历史日期列）
interface HistoryHealthItem {
  id: string;
  product_name: string;
  category: string;
  isCategory: boolean;
  [dateKey: string]: HealthStatus | any; // 动态历史日期字段，如 date_2025_07_30
}

// 当天实时状态数据项（用于"此刻"列）
interface TodayStatusItem {
  product_name: string;
  category: string;
  statuses: ('healthy' | 'info' | 'warning' | 'error' | 'critical')[];
  hasData: boolean;
  timestamp?: string;
  details?: string;
}

interface HealthStatus {
  statuses: ('healthy' | 'info' | 'warning' | 'error' | 'critical')[];
  hasData: boolean;
  timestamp?: string;
  details?: string;
}
```

#### 响应示例

```json
{
  "status": "success",
  "message": "获取数据成功",
  "data": {
    "items": [
      {
        "id": "category_计算",
        "product_name": "",
        "category": "计算",
        "isCategory": true,
        "date_2025_07_29": {
          "statuses": [],
          "hasData": false
        },
        "date_2025_07_28": {
          "statuses": [],
          "hasData": false
        }
      },
      {
        "id": "计算_0",
        "product_name": "云服务器",
        "category": "计算",
        "isCategory": false,
        "date_2025_07_29": {
          "statuses": ["error"],
          "hasData": true,
          "timestamp": "2025-07-29 10:00:00"
        },
        "date_2025_07_28": {
          "statuses": ["healthy"],
          "hasData": true,
          "timestamp": "2025-07-28 10:00:00"
        }
      }
    ],
    "today_status": [
      {
        "product_name": "云服务器",
        "category": "计算",
        "statuses": ["healthy", "warning"],
        "hasData": true,
        "timestamp": "2025-07-31 14:30:00"
      },
      {
        "product_name": "轻量应用服务器",
        "category": "计算",
        "statuses": ["healthy"],
        "hasData": true,
        "timestamp": "2025-07-31 14:30:00"
      }
    ],
    "dateRange": [
      "2025-07-30",
      "2025-07-29",
      "2025-07-28",
      "2025-07-27",
      "2025-07-26",
      "2025-07-25",
      "2025-07-24"
    ]
  }
}
```

## 数据结构定义

### 1. 数据源分离说明（v4.0.0 重要更新）

HealthPanel v4.0.0 将数据源分为两个独立部分：

#### 历史数据（items 字段）
- **用途**: 用于历史日期列的数据显示
- **时间范围**: 基于用户选定的日期范围（通常是7天）
- **数据特点**: 静态历史记录，不会实时更新
- **字段结构**: 包含 `date_YYYY_MM_DD` 格式的动态日期字段

#### 实时状态数据（today_status 字段）
- **用途**: 专门用于"此刻"列的数据显示
- **时间基准**: 当前系统时间所在日期的最新状态
- **数据特点**: 实时更新，反映当前最新健康状况
- **字段结构**: 简化的产品状态数组，包含产品名称、分类和状态信息

#### 状态统计逻辑变更
- **v4.0.0 之前**: 统计所有数据（历史 + 当前）
- **v4.0.0 之后**: 只统计 `today_status` 中的实时状态数据
- **优势**: 状态统计更准确反映当前实时健康状况

### 1. 健康状态枚举

| 状态值 | 中文名称 | 图标来源 | 颜色代码 | 优先级 | 说明 |
|--------|----------|----------|----------|--------|------|
| healthy | 正常 | 本地资源 | #67c23a | 1 | 系统运行正常 |
| info | 提示 | 本地资源 | #409eff | 2 | 一般性提示信息 |
| warning | 警告 | 本地资源 | #e6a23c | 3 | 需要关注的警告 |
| error | 严重 | 本地资源 | #f56c6c | 4 | 严重错误 |
| critical | 紧急 | 本地资源 | #a8071a | 5 | 紧急故障 |

### 2. 多状态支持

HealthPanel 组件支持产品的多状态显示，因为一个产品下可能包含多个实例，这些实例可能处于不同的健康状态。

#### 多状态数据结构
```typescript
interface HealthStatus {
  statuses: ('healthy' | 'info' | 'warning' | 'error' | 'critical')[];
  hasData: boolean;
  timestamp?: string;
  details?: string;
}
```

#### 多状态显示特性
- **单一状态**: 只显示一个图标（如：正常）
- **多重状态**: 显示多个图标（如：警告 + 紧急）
- **状态组合**: 最多显示2个状态图标，避免界面过于拥挤
- **图标排列**: 多个图标水平并排显示，间距2px
- **悬停提示**: 每个图标都有对应的状态名称提示

#### 状态统计逻辑
```javascript
// 多状态统计示例
function updateStatusStats() {
  // 遍历所有产品的所有状态
  products.forEach(product => {
    if (product.current && product.current.statuses) {
      product.current.statuses.forEach(status => {
        stats[status]++;
      });
    }
  });
}
```

### 3. 产品分类定义

```javascript
const productCategories = {
  '计算': {
    tagType: 'primary',
    products: ['云服务器', '轻量应用服务器', '专用宿主机', '弹性伸缩', '自动化助手']
  },
  '高性能计算': {
    tagType: 'success', 
    products: ['高性能计算', '批量计算']
  },
  '分布式云': {
    tagType: 'warning',
    products: ['分布式云', '本地专用集群', '专属可用区']
  },
  '容器': {
    tagType: 'info',
    products: ['容器服务', '弹性容器服务', '容器镜像服务']
  }
}
```

### 4. 表格数据结构

```typescript
interface TableRowData {
  id: string;                    // 唯一标识
  productName: string;           // 产品名称
  category: string;              // 产品分类
  isCategory: boolean;           // 是否为分类行
  rss: boolean;                  // 是否显示RSS图标
  current: HealthStatus;         // 当前状态（多状态数组）
  [dateKey: string]: HealthStatus; // 动态日期列（多状态数组）
}

interface DateColumn {
  key: string;                   // 日期键名，如 'day_0'
  label: string;                 // 显示标签，如 '07/30'
  date: string;                  // 完整日期，如 '2025-07-30'
}
```

### 5. 状态统计结构

```typescript
interface StatusStats {
  healthy: number;               // 正常状态数量
  info: number;                  // 提示状态数量
  warning: number;               // 警告状态数量
  error: number;                 // 严重状态数量
  critical: number;              // 紧急状态数量
}
```

## 业务逻辑说明

### 1. 数据初始化流程（v4.0.0 更新）

```mermaid
sequenceDiagram
    participant C as Component
    participant A as API
    participant D as Data

    C->>C: created() 生命周期
    C->>C: initializeDateColumns()
    C->>A: fetchHealthData()
    A->>D: 查询历史数据 + 实时状态
    D->>A: 返回分离的数据
    A->>C: 处理响应
    C->>C: processApiData()
    C->>C: transformApiDataToTableFormat() (历史数据)
    C->>C: processTodayStatusData() (实时状态)
    C->>C: updateStatusStats() (仅实时状态)
    C->>C: 渲染表格
```

### 2. 实时状态统计计算逻辑（v4.0.0 重构）

```javascript
// 新的状态统计逻辑：只统计实时状态数据
function updateStatusStats() {
  const stats = { healthy: 0, info: 0, warning: 0, error: 0, critical: 0 };

  // 确定要分析的数据源（只使用实时状态数据）
  let dataToAnalyze = this.todayStatusData;

  // 如果有产品筛选，则只统计筛选后的产品
  if (this.selectedProduct) {
    dataToAnalyze = this.todayStatusData.filter(
      item => item.productName === this.selectedProduct
    );
  }

  // 只统计实时状态数据
  dataToAnalyze.forEach(item => {
    if (item.hasData && item.statuses) {
      item.statuses.forEach(status => {
        stats[status]++;
      });
    }
  });

  return stats;
}
    
    // 统计历史日期状态（多状态数组）
    dateColumns.forEach(col => {
      if (row[col.key]?.hasData && row[col.key].statuses) {
        row[col.key].statuses.forEach(status => {
          stats[status]++;
        });
      }
    });
  });
  
  return stats;
}
```

### 3. 产品筛选功能

```javascript
// 筛选逻辑
function handleProductChange(selectedProduct) {
  if (selectedProduct) {
    // 筛选指定产品
    filteredTableData = tableData.filter(row => 
      row.productName === selectedProduct
    );
  } else {
    // 显示所有产品
    filteredTableData = [];
  }
  
  // 重新统计状态（支持多状态）
  updateStatusStats();
}
```

### 4. 日期导航功能

```javascript
// 日期导航逻辑
function navigateDate(days) {
  const currentDate = dayjs(selectedDate);
  const newDate = currentDate.add(days, 'day');
  const today = dayjs().format('YYYY-MM-DD');
  
  // 限制不能超过今天
  if (days > 0 && newDate.isAfter(today)) {
    selectedDate = today;
  } else {
    selectedDate = newDate.format('YYYY-MM-DD');
  }
  
  fetchHealthData();
}
```

## 组件配置

### 1. 状态图标配置

```javascript
const statusIcons = {
  healthy: '/static/icon/success.svg',
  info: '/static/icon/info.svg',
  warning: '/static/icon/warning.svg',
  error: '/static/icon/error.svg',
  critical: '/static/icon/critical.svg'
};
```

### 2. 多状态分布配置

```javascript
// 多状态数据生成逻辑
function generateRandomStatuses() {
  const statuses = [];
  const random = Math.random();

  // 70% 概率只有健康状态
  if (random < 0.7) {
    statuses.push('healthy');
  } else {
    // 30% 概率有异常状态，可能有多个状态
    const possibleStatuses = ['info', 'warning', 'error', 'critical'];
    const statusCount = Math.random() < 0.7 ? 1 : 2; // 70%概率1个状态，30%概率2个状态
    
    // 随机选择状态
    const shuffled = possibleStatuses.sort(() => 0.5 - Math.random());
    for (let i = 0; i < statusCount; i++) {
      if (shuffled[i]) {
        statuses.push(shuffled[i]);
      }
    }
  }

  return statuses;
}
```

### 3. 日期列配置

```javascript
// 生成最近7天的日期列
function initializeDateColumns() {
  const columns = [];
  // 从今天开始，按日期倒序排列（今天、昨天、前天...）
  for (let i = 0; i < 7; i++) {
    const date = dayjs().subtract(i, 'day');
    columns.push({
      key: `day_${i}`,
      label: date.format('MM/DD'),
      date: date.format('YYYY-MM-DD')
    });
  }
  return columns;
}
```

## 后端开发指南

### 1. 接口实现建议

#### 数据库表结构建议

```sql
-- 健康状态记录表
CREATE TABLE health_records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
  category VARCHAR(50) NOT NULL COMMENT '产品分类',
  status ENUM('healthy','info','warning','error','critical') NOT NULL COMMENT '健康状态',
  record_time DATETIME NOT NULL COMMENT '记录时间',
  details TEXT COMMENT '详细信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_product_time (product_name, record_time),
  INDEX idx_category_time (category, record_time)
);

-- 产品配置表
CREATE TABLE products (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '产品名称',
  category VARCHAR(50) NOT NULL COMMENT '产品分类',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 多状态接口实现示例

```python
from datetime import datetime, timedelta
from typing import List, Dict, Any

class HealthPanelService:
    def get_health_panel_data(self, date: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """获取健康面板数据"""
        
        # 1. 获取产品列表
        products = self.get_active_products()
        
        # 2. 获取指定时间范围的健康记录
        health_records = self.get_health_records(start_time, end_time)
        
        # 3. 生成日期范围（最近7天）
        date_range = self.generate_date_range(date, 7)
        
        # 4. 构建表格数据（支持多状态）
        items = self.build_table_data(products, health_records, date_range)
        
        # 5. 计算统计数据（支持多状态）
        statistics = self.calculate_statistics(health_records)
        
        return {
            "status": "success",
            "message": "获取数据成功",
            "data": {
                "items": items,
                "statistics": statistics,
                "dateRange": date_range
            }
        }
    
    def build_table_data(self, products: List, health_records: List, date_range: List) -> List[Dict]:
        """构建表格数据（支持多状态）"""
        items = []
        
        # 按分类分组
        categories = {}
        for product in products:
            if product.category not in categories:
                categories[product.category] = []
            categories[product.category].append(product)
        
        for category, category_products in categories.items():
            # 添加分类行
            category_row = {
                "id": f"category_{category}",
                "productName": "",
                "category": category,
                "isCategory": True,
                "rss": False,
                "current": {"statuses": [], "hasData": False}
            }
            
            # 为分类行添加空的日期列
            for date in date_range:
                date_key = f"day_{date_range.index(date)}"
                category_row[date_key] = {"statuses": [], "hasData": False}
            
            items.append(category_row)
            
            # 添加产品行
            for i, product in enumerate(category_products):
                product_row = {
                    "id": f"{category}_{i}",
                    "productName": product.name,
                    "category": product.category,
                    "isCategory": False,
                    "rss": True,
                    "current": self.get_current_statuses(product.name, health_records)
                }
                
                # 为产品行添加历史日期状态（多状态）
                for j, date in enumerate(date_range):
                    date_key = f"day_{j}"
                    product_row[date_key] = self.get_date_statuses(
                        product.name, date, health_records
                    )
                
                items.append(product_row)
        
        return items
    
    def get_current_statuses(self, product_name: str, health_records: List) -> Dict:
        """获取产品当前状态（多状态）"""
        current_records = [
            record for record in health_records 
            if record.product_name == product_name and 
            record.record_time >= datetime.now() - timedelta(hours=1)
        ]
        
        if not current_records:
            return {"statuses": [], "hasData": False}
        
        # 收集所有状态，去重
        statuses = list(set([record.status for record in current_records]))
        
        return {
            "statuses": statuses,
            "hasData": True,
            "timestamp": max(current_records, key=lambda x: x.record_time).record_time.isoformat()
        }
    
    def get_date_statuses(self, product_name: str, date: str, health_records: List) -> Dict:
        """获取产品指定日期状态（多状态）"""
        date_records = [
            record for record in health_records 
            if record.product_name == product_name and 
            record.record_time.date() == datetime.strptime(date, '%Y-%m-%d').date()
        ]
        
        if not date_records:
            return {"statuses": [], "hasData": False}
        
        # 收集该日期的所有状态，去重
        statuses = list(set([record.status for record in date_records]))
        
        return {
            "statuses": statuses,
            "hasData": True,
            "timestamp": max(date_records, key=lambda x: x.record_time).record_time.isoformat()
        }
```

### 2. 数据返回格式要求

#### 必填字段验证

```python
def validate_response_data(data: Dict) -> bool:
    """验证响应数据格式（多状态）"""
    required_fields = ['items', 'statistics', 'dateRange']
    
    for field in required_fields:
        if field not in data:
            raise ValueError(f"缺少必填字段: {field}")
    
    # 验证items结构
    for item in data['items']:
        if item.get('isCategory', False):
            # 分类行验证
            assert 'category' in item
            assert item['productName'] == ''
            assert item['current']['statuses'] == []
        else:
            # 产品行验证
            assert 'productName' in item
            assert 'current' in item
            assert 'statuses' in item['current']
            assert isinstance(item['current']['statuses'], list)
    
    return True
```

#### 状态枚举验证

```python
VALID_STATUSES = ['healthy', 'info', 'warning', 'error', 'critical']

def validate_statuses(statuses: List[str]) -> bool:
    """验证状态值（多状态）"""
    for status in statuses:
        if status not in VALID_STATUSES:
            return False
    return True
```

### 3. 错误处理规范

```python
class HealthPanelException(Exception):
    """健康面板异常基类"""
    pass

class DataNotFoundError(HealthPanelException):
    """数据未找到异常"""
    pass

class InvalidParameterError(HealthPanelException):
    """参数无效异常"""
    pass

def error_handler(func):
    """错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DataNotFoundError as e:
            return {
                "status": "error",
                "code": 404,
                "message": "数据未找到",
                "detail": str(e)
            }
        except InvalidParameterError as e:
            return {
                "status": "error", 
                "code": 400,
                "message": "参数错误",
                "detail": str(e)
            }
        except Exception as e:
            return {
                "status": "error",
                "code": 500,
                "message": "服务器内部错误",
                "detail": str(e)
            }
    return wrapper
```

### 4. 性能优化建议

#### 数据库查询优化

```sql
-- 使用索引优化多状态查询
CREATE INDEX idx_health_records_composite ON health_records(product_name, record_time, status);

-- 聚合查询统计多状态数据
SELECT 
  product_name,
  DATE(record_time) as record_date,
  GROUP_CONCAT(DISTINCT status) as statuses,
  COUNT(*) as record_count
FROM health_records 
WHERE record_time BETWEEN ? AND ?
GROUP BY product_name, DATE(record_time);
```

#### 缓存策略

```python
import redis
import json
from datetime import timedelta

class HealthPanelCache:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.cache_ttl = 300  # 5分钟缓存
    
    def get_cached_data(self, cache_key: str) -> Dict:
        """获取缓存数据"""
        cached = self.redis_client.get(cache_key)
        if cached:
            return json.loads(cached)
        return None
    
    def set_cached_data(self, cache_key: str, data: Dict):
        """设置缓存数据"""
        self.redis_client.setex(
            cache_key, 
            self.cache_ttl, 
            json.dumps(data, ensure_ascii=False)
        )
    
    def generate_cache_key(self, date: str, start_time: str, end_time: str) -> str:
        """生成缓存键"""
        return f"health_panel:{date}:{start_time}:{end_time}"
```

## 更新频率

- 组件默认每次切换日期时重新获取数据
- 建议后端接口响应时间控制在 500ms 以内
- 可考虑实现数据预加载和缓存机制

## 技术细节说明（v4.0.0）

### 1. "此刻"列数据更新频率和缓存策略

#### 数据更新频率
- **触发时机**: 每次调用 `fetchHealthData()` 时更新
- **更新场景**:
  - 组件初始化（mounted）
  - 用户切换日期（影响历史数据，同时刷新实时数据）
  - 手动刷新操作
- **建议频率**: 实时状态数据建议每5-10分钟自动刷新一次

#### 缓存策略
- **前端缓存**: 暂不实现，直接从API获取最新数据
- **后端缓存**: 建议实时状态数据缓存1-2分钟，历史数据可缓存更长时间
- **缓存失效**: 当有新的健康状态事件时，立即失效实时状态缓存

### 2. 实时状态数据与历史数据的数据结构差异

#### 实时状态数据结构（today_status）
```typescript
interface TodayStatusItem {
  product_name: string;        // 产品名称
  category: string;           // 产品分类
  statuses: string[];         // 状态数组
  hasData: boolean;          // 是否有数据
  timestamp?: string;        // 时间戳（可选）
}
```

#### 历史数据结构（items）
```typescript
interface HistoryHealthItem {
  product_name: string;
  category: string;
  isCategory: boolean;
  date_2025_07_30: {         // 动态日期字段
    statuses: string[];
    hasData: boolean;
    timestamp?: string;
  };
  // ... 更多日期字段
}
```

### 3. 状态统计在页面顶部的显示逻辑调整

#### 统计数据来源变更
- **v4.0.0 之前**: 统计所有表格数据（包括历史日期列）
- **v4.0.0 之后**: 只统计 `todayStatusData` 中的实时状态

#### 筛选功能影响
```javascript
// 产品筛选对状态统计的影响
function updateStatusStats() {
  let dataToAnalyze = this.todayStatusData;

  // 筛选逻辑：只影响实时状态统计
  if (this.selectedProduct) {
    dataToAnalyze = this.todayStatusData.filter(
      item => item.productName === this.selectedProduct
    );
  }

  // 统计逻辑保持不变
  // ...
}
```

### 4. 产品筛选功能对"此刻"列数据的影响

#### 筛选行为
- **历史数据**: 筛选影响历史日期列的显示
- **实时数据**: 筛选影响"此刻"列的显示和状态统计
- **统一性**: 确保筛选后的产品在所有列中保持一致

#### 实现逻辑
```javascript
function handleProductChange(selectedProduct) {
  if (selectedProduct) {
    // 筛选历史表格数据
    this.filteredTableData = this.tableData.filter(
      row => row.productName === selectedProduct
    );
  } else {
    this.filteredTableData = [];
  }

  // 重新计算状态统计（基于实时数据筛选）
  this.updateStatusStats();
}
```

## 注意事项

1. **数据分离**: v4.0.0 后历史数据和实时数据完全分离，确保API返回正确的数据结构
2. **状态枚举**: 严格使用预定义的5种状态值，避免数据不一致
3. **多状态处理**: 每个产品的状态字段必须是数组格式，即使只有一个状态
4. **时间格式**: 统一使用 'YYYY-MM-DD HH:mm:ss' 格式
5. **分类行处理**: 分类行的所有状态字段应设为空数组和 hasData: false
6. **图标资源**: 确保图标URL可访问，提供降级处理机制
7. **性能考虑**: 大量数据时考虑分页或虚拟滚动
8. **错误处理**: 提供友好的错误提示和降级显示
9. **多状态限制**: 建议每个产品最多显示2个状态图标，避免界面拥挤

## 版本历史

- **v4.0.0**: 🚀 重大更新
  - **"此刻"列重构**: 数据源从历史数据中的 `current` 字段改为独立的 `today_status` 字段
  - **状态统计优化**: 只统计实时状态数据，不再包含历史数据
  - **数据源分离**: 实时状态数据与历史数据完全分离
  - **API接口调整**: 新增 `today_status` 字段要求
  - **性能优化**: 减少不必要的数据处理和统计计算
- **v3.2.0**: 支持多状态显示，状态调整为5种，优化图标布局和统计逻辑
- **v3.1.0**: 健康状态扩展为6种，完善状态统计和图标显示
- **v3.0.0**: 移除表格滚动限制，改为完整内容显示
- **v2.x**: 表格滚动机制优化系列版本
- **v1.0.0**: 初始版本，基础健康状态展示功能

## 迁移指南（v3.x → v4.0.0）

### 后端开发者需要注意的变更

1. **API响应结构变更**:
   ```diff
   {
     "data": {
       "items": [...],           // 历史数据（移除 current 字段）
   +   "today_status": [...],    // 新增：当天实时状态数据
   -   "statistics": {...}       // 移除：不再返回预计算统计
     }
   }
   ```

2. **实时状态数据要求**:
   - 必须提供 `today_status` 字段
   - 包含所有产品的当天最新健康状态
   - 数据结构简化，只需产品名称、分类和状态信息

3. **历史数据调整**:
   - `items` 中移除 `current` 字段
   - 只保留 `date_YYYY_MM_DD` 格式的历史日期字段

### 前端开发者需要注意的变更

1. **状态统计逻辑变更**: 现在只统计实时状态，不包含历史数据
2. **数据获取方式**: "此刻"列数据从 `todayStatusData` 获取，不再从表格行数据获取
3. **筛选功能**: 产品筛选同时影响历史数据显示和实时状态统计
