# HealthPanel.vue API 参数修改记录

## 修改概述

修改了 HealthPanel.vue 组件中的 API 请求参数，将原来获取单日数据改为获取7天数据范围，确保与组件显示的7天日期列保持一致。

## 修改内容

### 1. API 请求参数修改

**修改前（单日数据）：**
```javascript
const params = {
  date: this.selectedDate,
  start_time: dayjs(this.selectedDate)
    .startOf('day')
    .format('YYYY-MM-DD HH:mm:ss'),
  end_time: dayjs(this.selectedDate)
    .endOf('day')
    .format('YYYY-MM-DD HH:mm:ss'),
}
```

**修改后（7天数据范围）：**
```javascript
// 计算7天数据范围：从选定日期往前推6天到选定日期
const endDate = dayjs(this.selectedDate)
const startDate = endDate.subtract(6, 'day')

const params = {
  date: this.selectedDate,
  start_time: startDate
    .startOf('day')
    .format('YYYY-MM-DD HH:mm:ss'),
  end_time: endDate
    .endOf('day')
    .format('YYYY-MM-DD HH:mm:ss'),
}
```

### 2. 日期列初始化逻辑修改

**修改前（基于今天）：**
```javascript
// 从今天开始，按日期倒序排列（今天、昨天、前天...）
for (let i = 0; i < 7; i++) {
  const date = dayjs().subtract(i, 'day')
  // ...
}
```

**修改后（基于选定日期）：**
```javascript
// 基于选定日期，从选定日期开始倒序排列7天
const endDate = dayjs(this.selectedDate)
for (let i = 0; i < 7; i++) {
  const date = endDate.subtract(i, 'day')
  // ...
}
```

### 3. 日期变化处理优化

在 `handleDateChange` 和 `navigateDate` 方法中添加了日期列的重新初始化：

```javascript
// 处理日期变化
handleDateChange(date) {
  this.selectedDate = date
  // 更新日期列以匹配新的选定日期
  this.initializeDateColumns()
  this.fetchHealthData()
}

// 日期导航功能
navigateDate(days) {
  // ... 日期计算逻辑
  
  // 更新日期列以匹配新的选定日期
  this.initializeDateColumns()
  this.fetchHealthData()
}
```

## 参数示例

### 当选定日期为 2024-01-07 时：

**API 请求参数：**
```json
{
  "date": "2024-01-07",
  "start_time": "2024-01-01 00:00:00",
  "end_time": "2024-01-07 23:59:59"
}
```

**日期列显示：**
- day_0: 01/07 (2024-01-07)
- day_1: 01/06 (2024-01-06)
- day_2: 01/05 (2024-01-05)
- day_3: 01/04 (2024-01-04)
- day_4: 01/03 (2024-01-03)
- day_5: 01/02 (2024-01-02)
- day_6: 01/01 (2024-01-01)

## 修改优势

1. **数据一致性**：API 请求的数据范围与界面显示的日期列完全匹配
2. **用户体验**：用户选择任意日期都能看到以该日期为结束点的7天数据
3. **逻辑清晰**：日期范围计算逻辑统一，便于维护
4. **功能完整**：保持了所有原有的日期导航和筛选功能

## 兼容性

- ✅ 保持了原有的日期格式：'YYYY-MM-DD HH:mm:ss'
- ✅ 保持了原有的参数结构，只修改了时间范围计算
- ✅ 保持了所有用户交互功能的正常工作
- ✅ 向后兼容，不影响其他组件功能

## 测试建议

1. **日期选择测试**：选择不同日期，验证API参数和日期列是否正确更新
2. **日期导航测试**：使用左右箭头导航，验证7天范围是否正确移动
3. **边界测试**：测试选择今天、过去日期等边界情况
4. **API调用测试**：验证修改后的参数能正确调用 `/alertscenter/api/v1/panel` 接口

## 调试信息

修改后的代码会在控制台输出以下调试信息：
- `请求参数 (7天范围):` - 显示API请求的完整参数
- `数据范围: YYYY-MM-DD 到 YYYY-MM-DD` - 显示请求的日期范围
- `日期列初始化完成:` - 显示生成的日期列数组

这些信息有助于开发和调试过程中验证参数的正确性。
